# 产品需求文档 (PRD)
## 2025年CIMC"西门子杯"中国智能制造挑战赛
### 工业嵌入式系统开发项目

---

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-16 |
| 负责人 | Emma (产品经理) |
| 项目类型 | 工业嵌入式系统开发 |
| 技术平台 | GD32 HAL + STM32F4xx |
| 开发约束 | sysFunction文件夹，应用层修改 |

---

## 1. 项目背景与问题陈述

### 1.1 竞赛背景
2025年CIMC"西门子杯"中国智能制造挑战赛，智能制造工程设计与应用类项目：工业嵌入式系统开发方向。要求设计并实现一个包含电压数据采集、处理、显示、存储的工业嵌入式系统。

### 1.2 核心问题
- **工业数据采集**: 需要实现高精度、实时的电压数据采集系统
- **数据处理与存储**: 需要支持多种数据格式转换和可靠的数据存储
- **人机交互**: 需要提供直观的显示界面和便捷的操作方式
- **系统可靠性**: 需要满足工业级的稳定性和实时性要求

### 1.3 技术约束
- **架构约束**: 所有逻辑层程序功能必须放在sysFunction文件夹下
- **修改范围**: 不改动底层和框架层，只对应用层进行修改
- **设计原则**: 采用低耦合设计理念，增强代码可扩展性和可维护性
- **现有基础**: 基于现有GD32 HAL工程架构进行扩展开发

---

## 2. 目标与成功指标

### 2.1 项目目标 (Objectives)

#### 主要目标
1. **功能完整性**: 实现题目要求的所有功能模块，通过完整评测流程
2. **系统稳定性**: 确保系统在工业环境下的可靠运行
3. **代码质量**: 实现高质量、可维护的代码架构
4. **性能优化**: 满足实时性要求，优化系统响应速度

#### 次要目标
1. **扩展性**: 为后续功能扩展预留接口和架构空间
2. **用户体验**: 提供直观的操作界面和清晰的状态反馈
3. **文档完整**: 提供完整的技术文档和使用说明

### 2.2 关键结果 (Key Results)

#### 功能指标
- ✅ 系统自检功能正确率 100%
- ✅ 时间设置和显示准确率 100%
- ✅ 配置参数读写成功率 100%
- ✅ 数据采集精度 ≥ 99.9%
- ✅ 数据存储完整性 100%

#### 性能指标
- ⏱️ LED闪烁周期精度 ± 10ms
- ⏱️ 按键响应时间 ≤ 100ms
- ⏱️ 串口命令响应时间 ≤ 200ms
- ⏱️ OLED显示刷新率 ≥ 1Hz
- ⏱️ 文件写入响应时间 ≤ 500ms

#### 质量指标
- 🔧 代码覆盖率 ≥ 90%
- 🔧 内存使用率 ≤ 80%
- 🔧 系统稳定运行时间 ≥ 24小时
- 🔧 错误恢复成功率 ≥ 95%

### 2.3 反向指标 (Counter Metrics)
- ❌ 系统响应延迟 ≤ 1秒
- ❌ 内存泄漏事件 = 0
- ❌ 数据丢失事件 = 0
- ❌ 系统崩溃事件 = 0

---

## 3. 用户画像与用户故事

### 3.1 目标用户
- **主要用户**: 竞赛评委和技术专家
- **次要用户**: 工业自动化工程师
- **使用场景**: 竞赛评测、工业数据采集应用

### 3.2 用户故事

#### 评委用户故事
```
作为竞赛评委，
我希望能够通过标准化的评测流程验证系统功能，
以便准确评估参赛作品的技术水平。
```

#### 工程师用户故事
```
作为工业自动化工程师，
我希望能够快速配置采样参数和查看实时数据，
以便监控工业设备的运行状态。
```

#### 维护人员用户故事
```
作为系统维护人员，
我希望能够查看系统日志和诊断信息，
以便快速定位和解决系统问题。
```

---

## 4. 功能规格详述

### 4.1 系统功能模块

#### 4.1.1 系统自检功能
**功能描述**: 通过串口命令"test"触发系统自检，检测关键硬件组件状态

**输入**: 串口命令 "test"

**输出格式**:
```
======system selftest======
flash............ok/error
TF card............ok/error
Flash ID: 0xCxxxxx
[TF card memory:xxxxx KB] 或 [can not find TF card]
RTC: 2025-01-01 01:00:50
======system selftest======
```

**业务逻辑**:
1. 检测SPI Flash ID读取状态
2. 检测TF卡存在性和容量
3. 获取当前RTC时间
4. 格式化输出检测结果

#### 4.1.2 设备ID管理
**功能描述**: 系统上电时显示设备ID，支持Flash存储和读取

**输出格式**:
```
====system init====
Device_ID:2025-CIMC-队伍编号
====system ready====
```

**业务逻辑**:
1. 系统上电时从Flash读取设备ID
2. 如果Flash中无设备ID，写入默认值
3. 串口输出设备ID信息

#### 4.1.3 时间设置功能
**功能描述**: 支持多种格式的时间字符串解析和RTC设置

**命令交互流程**:
```
输入: RTC Config
输出: Input Datetime
输入: 2025-01-01 12:00:30
输出: RTC Config success
      Time:2025-01-01 12:00:30

输入: RTC now  
输出: Current Time:2025-01-01 12:00:30
```

**支持的时间格式**:
- "2025-01-01 12:00:30"
- "2025-01-01 01-30-10"
- 其他包含年月日时分秒的格式

### 4.2 配置管理模块

#### 4.2.1 配置文件读取
**功能描述**: 从TF卡读取config.ini文件，解析变比和阈值参数

**文件格式**:
```ini
[Ratio]
Ch0 = 10.5
[Limit]  
Ch0 = 100
```

**命令交互**:
```
输入: conf
输出: config.ini file not found  (文件不存在时)
或
输出: Ratio=xxxx
      Limit=xxxx
      Config read success  (文件存在时)
```

#### 4.2.2 变比设置
**功能描述**: 设置电压变比参数，范围0-100，浮点数类型

**命令交互流程**:
```
输入: ratio
输出: Ratio=1.0
      Input value(0~100):
输入: 10.5
输出: ratio modified success
      Ratio=10.5

输入: 100.5  (超出范围)
输出: ratio invalid
      Ratio=1.0  (保持原值)
```

#### 4.2.3 阈值设置  
**功能描述**: 设置报警阈值，范围0-500，浮点数类型

**命令交互流程**:
```
输入: limit
输出: limit=1.0
      Input value(0~500):
输入: 50.12
输出: limit modified success
      limit=50.12

输入: 501.2  (超出范围)
输出: limit invalid
      limit=1.0  (保持原值)
```

#### 4.2.4 参数存储
**功能描述**: 将配置参数存储到Flash中，支持掉电保持

**命令交互**:
```
输入: config save
输出: ratio: 20.5
      Limit: 100.00
      Save parameters to flash

输入: config read  
输出: read parameters from flash
      ratio: 20.5
      Limit: 100.00
```

### 4.3 采样控制模块

#### 4.3.1 采样启停控制
**功能描述**: 支持串口命令和按键双重控制方式

**串口控制**:
```
输入: start
输出: Periodic Sampling
      Sample cycle:5s
      2025-01-01 00:30:05 ch0=10.5V
      2025-01-01 00:30:10 ch0=10.5V

输入: stop
输出: Periodic Sampling STOP
```

**按键控制**: KEY1按键控制采样启停，状态翻转

**LED指示**: LED1闪烁指示采样状态(1s周期)

**OLED显示**:
- 采样时: 第一行显示时间(hh:mm:ss)，第二行显示电压(xx.xx V)
- 空闲时: 第一行显示"system idle"，第二行为空

#### 4.3.2 周期调整
**功能描述**: 通过按键动态修改采样周期

**按键功能**:
- KEY2: 设置5s周期
- KEY3: 设置10s周期  
- KEY4: 设置15s周期

**输出示例**:
```
输出: sample cycle adjust: 10s
      2025-01-01 00:30:05 ch0=10.5V
      2025-01-01 00:30:15 ch0=10.5V
```

**持久化**: 周期设置断电重启后生效

#### 4.3.3 超限提示
**功能描述**: 当采样值超过设定阈值时触发报警

**报警机制**:
- LED2点亮
- 串口输出增加OverLimit标识

**输出格式**:
```
正常: 2025-01-01 00:30:05 ch0=10.5V
超限: 2025-01-01 00:30:05 ch0=10.5V OverLimit(10.00)!
```

### 4.4 数据处理模块

#### 4.4.1 数据编码转换
**功能描述**: 将时间戳和电压值转换为HEX格式

**编码规则**:
- 时间戳: 4字节Unix时间戳HEX (如 6774C4F5)
- 电压值: 4字节，分为整数部分2字节 + 小数部分2字节
  - 整数部分: 高位在前 (如 12V → 000C)
  - 小数部分: 0.5 * 65536 = 32768 → 8000

**示例**: 2025-01-01 12:30:45 ch0=12.5V → 6774C4F5000C8000

#### 4.4.2 Hide模式
**功能描述**: 切换数据显示格式

**命令交互**:
```
输入: hide
输出: 6774C4F5000C8000
      6774C4FA000C8000

输入: hide (超限时)
输出: 6774C4F5000C8000*
      6774C4FA000C8000*

输入: unhide
输出: 恢复到原有格式
```

### 4.5 数据存储模块

#### 4.5.1 采集数据存储
**存储位置**: TF卡/sample文件夹

**文件规则**:
- 每个文件存储10条数据
- 文件名: sampleData{datetime}.txt
- datetime格式: YYYYMMDDHHMMSS (如 sampleData20250101003010.txt)

#### 4.5.2 超阈值数据存储  
**存储位置**: TF卡/overLimit文件夹

**文件规则**:
- 每个文件存储10条数据
- 文件名: overLimit{datetime}.txt
- 格式与采集数据相同

#### 4.5.3 日志存储
**存储位置**: TF卡/log文件夹

**文件规则**:
- 每次上电新建一个文件
- 文件名: log{id}.txt (id从0开始自增)
- 记录所有操作内容和结果
- 上电次数记录在MCU中，持久化保存

#### 4.5.4 加密数据存储
**存储位置**: TF卡/hideData文件夹

**文件规则**:
- 每个文件存储10条数据
- 文件名: hideData{datetime}.txt
- 启用时sample文件夹不存储数据
- 同时存储未加密和加密数据用于校验

---

## 5. 范围定义

### 5.1 包含功能 (In Scope)

#### 核心功能
✅ 系统自检和设备ID管理  
✅ RTC时间设置和显示  
✅ 配置文件读取和参数管理  
✅ 电压数据采集和处理  
✅ 采样控制和周期调整  
✅ 超限检测和报警  
✅ 数据编码和格式转换  
✅ 多文件夹数据存储  
✅ 操作日志记录  
✅ OLED状态显示  

#### 扩展功能
✅ 双重控制方式(串口+按键)  
✅ 多种时间格式支持  
✅ 参数持久化存储  
✅ 错误处理和恢复  
✅ 系统状态指示  

### 5.2 排除功能 (Out of Scope)

❌ 网络通信功能  
❌ 图形化配置界面  
❌ 数据分析和统计功能  
❌ 远程监控和控制  
❌ 多通道数据采集  
❌ 高级数据加密算法  
❌ 实时数据库功能  
❌ Web服务接口  

---

## 6. 依赖与风险

### 6.1 内部依赖项

#### 硬件依赖
- STM32F4xx MCU正常工作
- SPI Flash存储器可用性
- TF卡插槽和文件系统支持
- OLED显示屏连接正常
- ADC通道和参考电压稳定
- 按键和LED硬件功能正常

#### 软件依赖  
- GD32 HAL库完整性
- LittleFS文件系统稳定性
- FATFS文件系统兼容性
- u8g2显示库功能正常
- 现有scheduler调度器可用
- Shell命令系统可扩展

### 6.2 外部依赖项

#### 开发环境
- Keil MDK或STM32CubeIDE开发环境
- 调试器和下载工具
- 串口调试工具
- TF卡和读卡器

#### 测试环境
- 可调电压源(滑动变阻器)
- 示波器或万用表
- 标准时间源
- 评测用的config.ini文件

### 6.3 潜在风险

#### 技术风险
🔴 **高风险**: 实时性要求可能影响系统稳定性
- **缓解措施**: 优化任务调度，使用硬件定时器确保精度

🟡 **中风险**: 文件系统操作可能导致数据丢失  
- **缓解措施**: 实现原子性写入和数据校验机制

🟡 **中风险**: 内存使用可能超出限制
- **缓解措施**: 使用静态内存分配，避免动态分配

#### 集成风险
🟡 **中风险**: 与现有系统集成可能产生冲突
- **缓解措施**: 充分分析现有代码，使用标准化接口

🟢 **低风险**: 硬件兼容性问题
- **缓解措施**: 基于现有硬件平台开发，复用已验证的驱动

#### 项目风险
🟡 **中风险**: 开发时间可能不足
- **缓解措施**: 采用增量开发，优先实现核心功能

🟢 **低风险**: 需求变更风险
- **缓解措施**: 题目要求明确，变更可能性较低

---

## 7. 发布初步计划

### 7.1 开发阶段规划

#### 第一阶段: 基础架构 (1-2天)
**目标**: 建立sysFunction模块基础框架
- 创建文件结构和数据类型定义
- 实现系统自检功能
- 实现设备ID管理
- 扩展RTC时间设置功能

**交付物**:
- sysFunction基础架构
- 系统自检功能
- 设备ID管理功能
- RTC时间设置功能

#### 第二阶段: 命令和配置 (1-2天)  
**目标**: 实现命令处理和配置管理
- 扩展Shell命令系统
- 实现配置文件读取
- 实现参数设置和验证
- 实现Flash参数存储

**交付物**:
- Shell命令扩展框架
- 配置管理功能
- 参数验证机制
- Flash存储功能

#### 第三阶段: 采样和处理 (2-3天)
**目标**: 实现核心采样和数据处理功能
- 实现采样控制功能
- 实现超限检测和报警
- 实现数据编码转换
- 实现OLED显示管理

**交付物**:
- 采样控制系统
- 超限检测功能
- 数据处理模块
- 显示管理功能

#### 第四阶段: 存储和优化 (1-2天)
**目标**: 完善数据存储和系统优化
- 实现多文件夹数据存储
- 实现日志记录功能
- 系统性能优化
- 错误处理完善

**交付物**:
- 数据存储系统
- 日志记录功能
- 性能优化版本
- 错误处理机制

#### 第五阶段: 集成测试 (1-2天)
**目标**: 系统集成和全面测试
- 系统集成和接口调试
- 功能测试和性能验证
- 按评测流程全面测试
- 文档整理和代码优化

**交付物**:
- 完整系统集成版本
- 测试报告和验证结果
- 技术文档和使用说明
- 最终发布版本

### 7.2 测试策略

#### 单元测试
- 每个模块独立功能测试
- 接口参数验证测试
- 边界条件和异常测试
- 性能基准测试

#### 集成测试
- 模块间接口测试
- 数据流完整性测试
- 系统状态一致性测试
- 并发操作测试

#### 系统测试
- 完整评测流程测试
- 长时间稳定性测试
- 压力测试和边界测试
- 用户场景模拟测试

### 7.3 发布标准

#### 功能完整性
- 所有题目要求功能100%实现
- 所有命令交互格式正确
- 所有数据存储功能正常
- 所有显示和指示功能正常

#### 性能要求
- 实时性指标满足要求
- 内存使用在安全范围内
- 系统响应时间符合标准
- 长时间运行稳定

#### 质量标准
- 代码风格一致性
- 注释和文档完整性
- 错误处理完善性
- 测试覆盖率达标

---

## 8. 技术规范附录

### 8.1 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        sysFunction 模块                      │
├─────────────────────────────────────────────────────────────┤
│  sys_core    │  sys_rtc     │  sys_config  │  sys_command   │
│  (自检/ID)   │  (时间处理)   │  (配置管理)   │  (命令处理)     │
├─────────────────────────────────────────────────────────────┤
│  sys_sampling │ sys_data_process │ sys_storage │ sys_display │
│  (采样控制)    │  (数据处理)      │  (数据存储)  │  (显示管理)  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      现有系统架构                            │
├─────────────────────────────────────────────────────────────┤
│  scheduler  │  shell_app  │  usart_app  │  adc_app        │
│  (任务调度)  │  (命令行)   │  (串口通信)  │  (ADC采集)       │
├─────────────────────────────────────────────────────────────┤
│  led_app    │  btn_app    │  oled_app   │  rtc_app        │
│  (LED控制)  │  (按键处理)  │  (OLED显示) │  (RTC时钟)       │
├─────────────────────────────────────────────────────────────┤
│  LittleFS   │  FATFS      │  u8g2       │  WouoUI         │
│  (Flash文件) │  (TF卡文件) │  (显示库)   │  (界面框架)      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      硬件抽象层                              │
├─────────────────────────────────────────────────────────────┤
│  UART1      │  ADC1       │  SPI2       │  SDIO           │
│  (串口通信)  │  (电压采集)  │  (Flash)    │  (TF卡)         │
├─────────────────────────────────────────────────────────────┤
│  I2C1       │  TIM3/6/14  │  GPIO       │  DMA            │
│  (OLED)     │  (定时器)   │  (按键/LED)  │  (数据传输)      │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 数据流图

```
[串口输入] ──→ [命令解析] ──→ [功能模块] ──→ [数据处理] ──→ [存储/显示]
     │              │              │              │              │
     ↓              ↓              ↓              ↓              ↓
[按键输入] ──→ [按键处理] ──→ [采样控制] ──→ [状态更新] ──→ [LED/OLED]
     │              │              │              │              │
     ↓              ↓              ↓              ↓              ↓
[定时器] ────→ [采样触发] ──→ [ADC读取] ──→ [数据处理] ──→ [文件存储]
```

### 8.3 状态机设计

```
系统状态机:
INIT ──→ IDLE ──→ SAMPLING ──→ IDLE
  │        │         │          │
  ↓        ↓         ↓          ↓
ERROR ←── ERROR ←── ERROR ←── ERROR

显示模式:
IDLE_DISPLAY ←──→ SAMPLING_DISPLAY
      │                  │
      ↓                  ↓
"system idle"    "时间 + 电压值"

数据模式:
NORMAL_MODE ←──→ HIDE_MODE
      │              │
      ↓              ↓
"标准格式"      "HEX格式"
```

### 8.4 接口定义

```c
// 系统核心接口
sys_result_t sys_core_init(void);
sys_result_t sys_core_selftest(void);
sys_result_t sys_device_id_init(void);

// RTC扩展接口  
sys_result_t sys_rtc_parse_datetime(const char* datetime_str, sys_datetime_t* dt);
sys_result_t sys_rtc_set_datetime(sys_datetime_t* dt);
uint32_t sys_rtc_to_unix(sys_datetime_t* dt);

// 配置管理接口
sys_result_t sys_config_read_ini(void);
sys_result_t sys_config_set_ratio(float ratio);
sys_result_t sys_config_set_limit(float limit);
sys_result_t sys_config_save_flash(void);

// 采样控制接口
sys_result_t sys_sampling_start(void);
sys_result_t sys_sampling_stop(void);
sys_result_t sys_sampling_set_period(uint32_t period_ms);
float sys_sampling_get_voltage(void);

// 数据处理接口
uint32_t sys_data_unix_timestamp(sys_datetime_t* dt);
sys_result_t sys_data_voltage_encode(float voltage, uint16_t* int_part, uint16_t* frac_part);
sys_result_t sys_data_set_hide_mode(bool hide_mode);

// 存储管理接口
sys_result_t sys_storage_init(void);
sys_result_t sys_storage_save_sample(sys_sample_data_t* data);
sys_result_t sys_storage_save_log(const char* log_msg);

// 显示管理接口
sys_result_t sys_display_init(void);
sys_result_t sys_display_set_mode(sys_display_mode_t mode);
sys_result_t sys_display_update(void);
```

---

## 9. 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-01-16 | 初始版本创建，完整PRD文档 | Emma |

---

**文档结束**

> 本PRD文档基于深入的代码库分析和系统架构研究，确保技术方案的可行性和与现有系统的完美集成。所有功能设计严格遵循题目要求，采用低耦合、高内聚的设计原则，为后续开发提供清晰的指导和标准。